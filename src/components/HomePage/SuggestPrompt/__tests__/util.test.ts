import {getRankingCardsWithInterference} from '../cardRanking';


describe('getRankingCardsWithInterference', () => {
    // 模拟卡片数据
    const createMockCard = (adviceKey: string) => ({
        params: {adviceKey},
        key: adviceKey,
        id: adviceKey,
    });

    // 测试数据
    const testData = {
        queryoutline: [
            'addCreativeOptimizeCtr',
            'fixAbnormalTransTypes',
            'addCampaign',
            'addEcpcConversionOptEffect',
            'aiMax',
            'addUnitProductCategory',
            'addAdGroup',
            'fcAdgroupAutoTargeting',
            'removeInefficientKeyword',
            'addDeepConversionOptEffect',
            'addUnitProductTravel',
            'modBudgetForDisplay',
        ],
        ranking: [
            'fixAbnormalTransTypes',
            'addMissTransFroms',
            'removeUncorrelatedKeyword',
            'modAuditRejectMaterial',
            'addUnitProductTravel',
            'addCampaign',
            'addAdGroup',
            'addEcpcConversionOptEffect',
            'addKeyword',
            'fcPcOptimize',
            'removeInefficientKeyword',
            'addUnitProductCategory',
            'picRecommends',
            'textRecommends',
            'addCreativeOptimizeCtr',
            'modLandingPageUrl',
            'modBudgetForDisplay',
            'icgLocEnough',
            'optimizeLandingPageQuality',
            'fcKeywordRanking',
            'addCreativeImageOptimizeCtr',
            'modCampaignEquipmentConversion',
            'addTransTypes',
            'addDeepConversionOptEffect',
            'modFeedBudgetForConversion',
            'aiMax',
            'fcAdgroupAutoTargeting',
            'dynamicImageOptimize',
            'modBudgetForConversion',
            'dynamicTextOptimize',
            'add',
            'addProjectSharedBudget',
            'learn',
        ],
        interfereIndexs: [4, 5, 6, 7],
        interfereList: [
            'modBalanceFeed',
            'modBudgetForDisplay',
            'modAccountBudgetFeed',
            'modCampaignBudgetFeed',
            'modCampaignEquipmentConversion',
            'consumptionDecline',
            'addKeyword',
            'modBudgetForConversion',
            'modFeedBudgetForConversion',
            'addFeedCreativeConversion',
            'addFeedCreativePictureConversion',
            'addFeedCreativeVideoConversion',
            'addUnitProductCategory',
            'addUnitProductTravel',
            'addFeedProductCategory',
            'addMissTransFroms',
            'icgLocEnoug',
            'addCampaign',
            'addAdGroup',
            'modAuditRejectMaterial',
            'modAuditRejectMaterialFeed',
            'manageMonitorUrl',
            'setOcpcConversionTrack',
            'fixAbnormalTransTypes',
            'modExpiredMaterialFeed',
            'removeConflictNgtWord',
            'modRecommendCampaignBudget',
            'fcAdgroupAutoTargeting',
            'removeInefficientKeyword',
            'removeIneffectiveNgtWord',
            'removeConversionNgtWord',
            'addDeepConversionOptEffect',
            'removeDuplicateNgtWord',
            'removeUncorrelatedKeyword',
            'modLandingPageUrl',
            'optimizeLandingPageQuality',
            'modOcpcPriceOptimizeConversion',
            'modPriceOptimizeClick',
            'modPriceOptimizeEcpcConversion',
            'addEcpcConversionOptEffect',
            'addTransTypes',
            'dynamicTextOptimize',
            'addCreativeImageOptimizeCtr',
            'dynamicImageOptimize',
            'addProjectSharedBudget',
            'remainClueConversion',
            'addCreativeOptimizeCtr',
            'addCreativeVideoOptimizeCtr',
            'improveOcpcBid',
            'improveAccountBudget',
        ],
    };

    // 创建模拟卡片数组
    const mockCards = testData.queryoutline.map(createMockCard);

    test('should apply interference logic correctly', () => {
        const result = getRankingCardsWithInterference(
            mockCards,
            testData.ranking,
            testData.interfereIndexs,
            testData.interfereList
        );

        // 1. ranking后的卡片顺序
        // 'fixAbnormalTransTypes',
        // 'addUnitProductTravel',
        // 'addCampaign',
        // 'addAdGroup',
        // 'addEcpcConversionOptEffect',
        // 'removeInefficientKeyword',
        // 'addUnitProductCategory',
        // 'addCreativeOptimizeCtr',
        // 'modBudgetForDisplay',
        // 'addDeepConversionOptEffect',
        // 'aiMax',
        // 'fcAdgroupAutoTargeting'


        // 2. 经过interference后的卡片顺序
        // 'fixAbnormalTransTypes',
        // 'addUnitProductTravel',
        // 'addCampaign',
        // 'addAdGroup',
        // 4-7位置
        // 4: modBudgetForDisplay
        // 5: addUnitProductCategory
        // 6: fcAdgroupAutoTargeting
        // 7: removeInefficientKeyword
        // 'addEcpcConversionOptEffect',
        // 'addCreativeOptimizeCtr',
        // 'addDeepConversionOptEffect',
        // 'aiMax',
        expect(result.map(it => it.key)).toMatchInlineSnapshot();

    });

});
