/* eslint-disable complexity */
/*
 * @file 卡片排序逻辑
 * <AUTHOR>
 * @date 2025-08-25 15:29:57
 */

function getRankingCards(cards: any[], ranking: string[]) {
    const copyCards = [...cards];
    return copyCards.sort((a, b) => {
        const getIndex = (card: any) => {
            const key = card.params?.adviceKey || card.key;
            const idx = ranking.indexOf(key);
            return idx === -1 ? Number.MAX_SAFE_INTEGER : idx; // 未命中则返回最大值
        };
        return getIndex(a) - getIndex(b);
    });
}


/**
 * 实现干预排序逻辑
 * @param cards 所有卡片
 * @param ranking 基础排序数组
 * @param interfereIndexs 干预位置数组
 * @param interfereList 干预卡片排序数组
 * @returns 最终排序后的卡片数组
 */
export function getRankingCardsWithInterference(
    cards: any[],
    ranking: string[],
    interfereIndexs: number[] = [],
    interfereList: string[] = []
) {
    // 如果没有干预数据，使用原有排序逻辑
    if (interfereIndexs.length === 0 || interfereList.length === 0) {
        return getRankingCards(cards, ranking);
    }

    // 先按照ranking排序所有召回卡片形成A集合
    const setA = getRankingCards(cards, ranking);

    // 按照干预数据interfereList排序所有召回卡片形成B集合
    const setB = getRankingCards(cards, interfereList);

    console.log('[ setA ] >', {setA: setA.map(it => it.key), setB: setB.map(it => it.key)});

    // 创建最终结果数组，初始为A集合的副本
    const result = [...setA];

    // 依次处理每个需要干预的位置
    for (const interfereIndex of interfereIndexs) {
        // 确保干预位置在有效范围内
        if (interfereIndex < 0 || interfereIndex >= result.length) {
            continue;
        }

        // 从B集合头部开始查找可以插入的卡片
        for (const cardFromB of setB) {
            const cardKey = cardFromB.params?.adviceKey || cardFromB.key;

            // 检查这张卡是否已经在interfereIndex位置及之前出现过
            let alreadyExists = false;
            for (let i = 0; i <= interfereIndex; i++) {
                const existingCardKey = result[i]?.params?.adviceKey || result[i]?.key;
                if (existingCardKey === cardKey) {
                    alreadyExists = true;
                    break;
                }
            }

            // 如果卡片已经在interfereIndex位置及之前出现过，继续看B集合后续的卡
            if (alreadyExists) {
                continue;
            }

            // 找到了可以插入的卡片
            // 先从result中找到这张卡的当前位置
            const cardIndex = result.findIndex(card =>
                (card.params?.adviceKey || card.key) === cardKey
            );

            if (cardIndex !== -1 && cardIndex !== interfereIndex) {
                // 从原位置移除卡片
                const [cardToMove] = result.splice(cardIndex, 1);
                // 插入到干预位置
                // 如果原位置在干预位置之前，插入时索引不需要调整
                // 如果原位置在干预位置之后，插入时索引需要调整
                const insertIndex = cardIndex < interfereIndex ? interfereIndex - 1 : interfereIndex;
                result.splice(insertIndex, 0, cardToMove);
            }

            // 找到一张卡片后就处理下一个干预位置
            break;
        }
    }

    return result;
}
